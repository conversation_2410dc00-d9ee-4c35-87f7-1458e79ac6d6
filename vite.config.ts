import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      'mind-elixir': path.resolve(__dirname, '../lib/mind-elixir-core-feature-v5/src'),
      '@mind-elixir/node-menu': path.resolve(__dirname, '../lib/node-menu-5.0.0'),
    },
    extensions: ['.js', '.ts', '.jsx', '.tsx', '.json']
  }
})