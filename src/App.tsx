import './App.css'
import MindElixir from 'mind-elixir'
import nodeMenu from '@mind-elixir/node-menu'
import {useEffect} from "react";

const encodeHTML = (s: string) => {
  return s.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/"/g, '&quot;')
}

function App() {

  useEffect(() => {
    const mind = new MindElixir({el: '#map'})
    mind.install(nodeMenu)
    const data = MindElixir.new('new topic')
    mind.init(data)
    // mind.reshapeNode()
    mind.bus.addListener("operation", (operation) => {
      console.log(operation);
      if (operation.name === 'updateMemo') {
        console.log('触发备注更新，', operation.obj)
        console.log('打印data，', mind.nodeData)
        const tpc = document.querySelector(`[data-nodeid="me${operation.obj.id}"]`)!;
        const memoEl = document.createElement('span')
        memoEl.className = 'memo'
        memoEl.innerHTML = `<span>${encodeHTML(operation.obj.memo)}</span>`
        tpc.prepend(memoEl)
      }
    });
  }, [])

  return (
    <>
      <div id="map" style={{height: '100vh', width: '100vw'}}>
      </div>
    </>
  )
}

export default App