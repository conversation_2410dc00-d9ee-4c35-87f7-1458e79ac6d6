<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta http-equiv="X-UA-Compatible" content="ie=edge" />
  <title>Mind Elixir</title>
  <link rel="stylesheet" href="./katex.css">
  <link rel="stylesheet" href="./index.css">
  <style>
    /* test tailwind compatibility */
    *,
    ::before,
    ::after {
      box-sizing: border-box;
      border-width: 0;
      border-style: solid;
      border-color: #e5e7eb;
    }

    #map,
    #map2 {
      margin-top: 50px;
      height: 500px;
      width: 800px;
    }
  </style>
</head>

<body>
  <div id="map"></div>
  <!-- test transform compatibility -->
  <div style="transform: translateX(30px) translateY(20px);">
    <div id="map2"></div>
  </div>
  <div id="ssr"></div>
  <script type="module" src="/src/dev.ts"></script>
</body>

</html>