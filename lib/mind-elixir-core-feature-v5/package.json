{"name": "mind-elixir", "version": "5.0.0-beta.45", "type": "module", "description": "Mind elixir is a free open source mind map core.", "keywords": ["mind-elixir", "mindmap", "dom", "visualization"], "scripts": {"prepare": "husky install", "lint": "eslint --cache --max-warnings 0  \"src/**/*.{js,json,ts}\" --fix", "dev": "vite", "build": "node build.js && tsc", "tsc": "tsc", "preview": "vite preview", "test": "playwright test", "test:ui": "playwright test --ui", "doc": "api-extractor run --local --verbose", "doc:md": "api-documenter markdown --input-folder ./api --output-folder ./md", "beta": "npm run build && npm publish --tag beta"}, "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/MindElixir.js", "require": "./dist/MindElixir.js"}, "./lite": {"import": "./dist/MindElixirLite.iife.js"}, "./example": {"types": "./dist/types/exampleData/1.d.ts", "import": "./dist/example.js", "require": "./dist/example.js"}, "./LayoutSsr": {"types": "./dist/types/utils/LayoutSsr.d.ts", "import": "./dist/LayoutSsr.js", "require": "./dist/LayoutSsr.js"}, "./readme.md": "./readme.md", "./package.json": "./package.json", "./style": "./dist/style.css"}, "typesVersions": {"*": {"example": ["./dist/types/exampleData/1.d.ts"]}}, "main": "dist/MindElixir.js", "types": "dist/types/index.d.ts", "lint-staged": {"src/**/*.{ts,js}": ["eslint --cache --fix"], "src/**/*.{json,less}": ["prettier --write"]}, "files": ["package.json", "dist"], "repository": {"type": "git", "url": "https://github.com/ssshooter/mind-elixir-core"}, "homepage": "https://mind-elixir.com/", "author": "ssshooter", "license": "MIT", "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@microsoft/api-documenter": "^7.25.21", "@microsoft/api-extractor": "^7.47.11", "@playwright/test": "^1.44.1", "@rollup/plugin-strip": "^3.0.4", "@types/node": "^20.14.2", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@viselect/vanilla": "3.9.0", "@zumer/snapdom": "^1.3.0", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "less": "^4.2.0", "lint-staged": "^13.3.0", "prettier": "2.8.4", "typescript": "^5.4.5", "vite": "^4.5.3"}}