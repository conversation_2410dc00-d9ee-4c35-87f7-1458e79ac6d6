.map-container .node-menu {
  position: absolute;
  right: 20px;
  top: 20px;
  background: #fff;
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  width: 240px;
  box-sizing: border-box;
  padding: 0 15px 15px;
  transition: 0.3s all;
  &.close {
    height: 30px;
    width: 46px;
    overflow: hidden;
  }
  .button-container {
    padding: 3px 0;
    direction: rtl;
  }
  #nm-tag {
    margin-top: 20px;
  }
  .nm-fontsize-container {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    div {
      height: 36px;
      width: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
      background-color: white;
      color: tomato;
      border-radius: 100%;
    }
  }
  .nm-fontcolor-container {
    margin-bottom: 10px;
  }
  input,
  textarea {
    background: #f7f9fa;
    border: 1px solid #dce2e6;
    border-radius: 3px;
    padding: 5px;
    margin: 10px 0;
    width: 100%;
    box-sizing: border-box;
  }
  textarea {
    resize: none;
  }
  .split6 {
    display: inline-block;
    width: 16.66%;
    margin-bottom: 5px;
  }
  .palette {
    border-radius: 100%;
    width: 21px;
    height: 21px;
    border: 1px solid #edf1f2;
    margin: auto;
  }
  .nmenu-selected,
  .palette:hover {
    box-shadow: tomato 0 0 0 2px;
    background-color: #c7e9fa;
  }
  .size-selected {
    background-color: tomato !important;
    border-color: tomato;
    fill: white;
    color: white;
    svg {
      color: #fff;
    }
  }
  .bof {
    text-align: center;
    span {
      display: inline-block;
      font-size: 14px;
      border-radius: 4px;
      padding: 2px 5px;
    }
    .selected {
      background-color: tomato;
      color: white;
    }
  }
}
