{"name": "@mind-elixir/node-menu", "version": "5.0.0", "type": "module", "files": ["dist"], "main": "./dist/node-menu.umd.cjs", "module": "./dist/node-menu.js", "exports": {".": {"import": "./dist/node-menu.js", "require": "./dist/node-menu.umd.cjs"}, "./dist/style.css": {"import": "./dist/style.css", "require": "./dist/style.css"}}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"less": "^4.1.3", "mind-elixir": "^5.0.1", "vite": "^3.0.0"}, "peerDependencies": {"mind-elixir": ">5.0.0"}}