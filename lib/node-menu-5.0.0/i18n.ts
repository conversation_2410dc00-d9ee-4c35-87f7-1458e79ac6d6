const cn = {
  addChild: '插入子节点',
  addParent: '插入父节点',
  addSibling: '插入同级节点',
  removeNode: '删除节点',
  focus: '专注',
  cancelFocus: '取消专注',
  moveUp: '上移',
  moveDown: '下移',
  link: '连接',
  clickTips: '请点击目标节点',

  font: '文字',
  background: '背景',
  tag: '标签',
  icon: '图标',
  tagsSeparate: '多个标签半角逗号分隔',
  iconsSeparate: '多个图标半角逗号分隔',
  url: 'URL',
}
export default {
  cn,
  zh_CN: cn,
  zh_TW: {
    addChild: '插入子節點',
    addParent: '插入父節點',
    addSibling: '插入同級節點',
    removeNode: '刪除節點',
    focus: '專注',
    cancelFocus: '取消專注',
    moveUp: '上移',
    moveDown: '下移',
    link: '連接',
    clickTips: '請點擊目標節點',

    font: '文字',
    background: '背景',
    tag: '標簽',
    icon: '圖標',
    tagsSeparate: '多個標簽半角逗號分隔',
    iconsSeparate: '多個圖標半角逗號分隔',
    url: 'URL',
  },
  de: {
    addChild: 'Kindknoten hinzufügen',
    addParent: 'Elternknoten hinzufügen',
    addSibling: 'Geschwisterknoten hinzufügen',
    removeNode: 'Knoten entfernen',
    focus: 'Fokus-Modus',
    cancelFocus: 'Fokus-Modus abbrechen',
    moveUp: 'Nach oben bewegen',
    moveDown: 'Nach unten bewegen',
    link: 'Verbinden',
    clickTips: 'Bitte den Zielknoten anklicken',

    font: 'Schriftart',
    background: 'Hintergrund',
    tag: 'Tag',
    icon: 'Icon',
    tagsSeparate: 'Durch Komma getrennte Tags',
    iconsSeparate: 'Durch Komma getrennte Icons',
    url: 'URL',
  },
  en: {
    addChild: 'Add child',
    addParent: 'Add parent',
    addSibling: 'Add sibling',
    removeNode: 'Remove node',
    focus: 'Focus Mode',
    cancelFocus: 'Cancel Focus Mode',
    moveUp: 'Move up',
    moveDown: 'Move down',
    link: 'Link',
    clickTips: 'Please click the target node',

    font: 'Font',
    background: 'Background',
    tag: 'Tag',
    icon: 'Icon',
    tagsSeparate: 'Separate tags by comma',
    iconsSeparate: 'Separate icons by comma',
    url: 'URL',
  },
  ru: {
    addChild: 'Добавить дочерний элемент',
    addParent: 'Добавить родительский элемент',
    addSibling: 'Добавить на этом уровне',
    removeNode: 'Удалить узел',
    focus: 'Режим фокусировки',
    cancelFocus: 'Отменить режим фокусировки',
    moveUp: 'Поднять выше',
    moveDown: 'Опустить ниже',
    link: 'Ссылка',
    clickTips: 'Пожалуйста, нажмите на целевой узел',
    font: 'Цвет шрифта',
    background: 'Цвет фона',
    tag: 'Тег',
    icon: 'Иконка',
    tagsSeparate: 'Разделяйте теги запятой',
    iconsSeparate: 'Разделяйте иконки запятой',
  },
  ja: {
    addChild: '子ノードを追加する',
    addParent: '親ノードを追加します',
    addSibling: '兄弟ノードを追加する',
    removeNode: 'ノードを削除',
    focus: '集中',
    cancelFocus: '集中解除',
    moveUp: '上へ移動',
    moveDown: '下へ移動',
    link: 'コネクト',
    clickTips: 'ターゲットノードをクリックしてください',

    font: 'フォント',
    background: 'バックグラウンド',
    tag: 'タグ',
    icon: 'アイコン',
    tagsSeparate: '複数タグはカンマ区切り',
    iconsSeparate: '複数アイコンはカンマ区切り',
    url: 'URL',
  },
  pt: {
    addChild: 'Adicionar item filho',
    addParent: 'Adicionar item pai',
    addSibling: 'Adicionar item irmao',
    removeNode: 'Remover item',
    focus: 'Modo Foco',
    cancelFocus: 'Cancelar Modo Foco',
    moveUp: 'Mover para cima',
    moveDown: 'Mover para baixo',
    link: 'Link',
    clickTips: 'Favor clicar no item alvo',

    font: 'Fonte',
    background: 'Cor de fundo',
    tag: 'Tag',
    icon: 'Icone',
    tagsSeparate: 'Separe tags por virgula',
    iconsSeparate: 'Separe icones por virgula',
    url: 'URL',
  },
}
